<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('productinfo', function (Blueprint $table) {
            $table->integer('sold_count_base')->default(0)->comment('已售出起始數值')->after('category_type');
            $table->integer('sold_count_actual')->default(0)->comment('實際已售出數量(從訂單計算)')->after('sold_count_base');
            $table->integer('sold_count_display')->default(0)->comment('顯示的已售出數量(max(起始數值, 實際數量))')->after('sold_count_actual');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('productinfo', function (Blueprint $table) {
            $table->dropColumn(['sold_count_base', 'sold_count_actual', 'sold_count_display']);
        });
    }
};
